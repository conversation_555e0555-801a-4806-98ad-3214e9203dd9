from typing import Dict, Any, Optional
import logging
from datetime import datetime
from ..models.trading_decision import TradingDecision, TradeType, TradeTimeframe
from ..clients.gemini_client import GeminiClient


class AIAnalyzer:
    def __init__(self, logger: logging.Logger = None):
        """Khởi tạo AI Analyzer.

        Args:
            logger: Logger instance
        """
        self.logger = logger or logging.getLogger(__name__)
        self.gemini_client = GeminiClient(logger)

    def analyze_market(
        self, symbol: str, indicators: Dict[str, Any], current_price: float
    ) -> TradingDecision:
        """Phân tích thị trường bằng AI.

        Args:
            symbol: Cặp tiền tệ
            indicators: Dict chứa các chỉ báo kỹ thuật
            current_price: <PERSON>i<PERSON> hiện tại

        Returns:
            TradingDecision chứa quyết định giao dịch
        """
        try:
            # Gọi Gemini API để phân tích
            ai_analysis = self.gemini_client.analyze_market(
                symbol, indicators, current_price
            )

            # Chuyển đổi kết quả thành TradingDecision
            decision = TradingDecision(
                symbol=symbol,
                decision=TradeType(ai_analysis["decision"]),
                timeframe=TradeTimeframe(ai_analysis["timeframe"]),
                confidence=float(ai_analysis["confidence"]),
                entry_price=current_price,
                stop_loss=ai_analysis.get("stop_loss"),
                take_profit=ai_analysis.get("take_profit"),
                reasoning=ai_analysis["reasoning"],
                indicators=indicators,
                timestamp=datetime.now(),
            )

            return decision

        except Exception as e:
            self.logger.error(f"Lỗi phân tích AI: {str(e)}")
            return TradingDecision(
                symbol=symbol,
                decision=TradeType.HOLD,
                timeframe=TradeTimeframe.SHORT_TERM,
                confidence=0.0,
                entry_price=current_price,
                reasoning=f"Lỗi phân tích AI: {str(e)}",
                indicators=indicators,
                timestamp=datetime.now(),
            )
