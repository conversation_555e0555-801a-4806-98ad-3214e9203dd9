from typing import Dict, Any, List
import logging
from ..models.trading_decision import TradingDecision, TradeType, TradeTimeframe


class IndicatorAnalyzer:
    def __init__(self, logger: logging.Logger = None):
        self.logger = logger or logging.getLogger(__name__)

    def analyze_indicators(
        self, symbol: str, indicators: Dict[str, Any], current_price: float
    ) -> TradingDecision:
        """Phân tích các chỉ báo và đưa ra quyết định giao dịch."""
        try:
            # Khởi tạo các biến phân tích
            signals = []
            confidence_scores = []
            reasoning_points = []

            # Phân tích RSI
            if "RSI" in indicators:
                rsi_decision, rsi_confidence, rsi_reason = self._analyze_rsi(
                    indicators["RSI"]
                )
                signals.append(rsi_decision)
                confidence_scores.append(rsi_confidence)
                reasoning_points.append(rsi_reason)

            # Phân tích MACD
            if "MACD" in indicators:
                macd_decision, macd_confidence, macd_reason = self._analyze_macd(
                    indicators["MACD"]
                )
                signals.append(macd_decision)
                confidence_scores.append(macd_confidence)
                reasoning_points.append(macd_reason)

            # Phân tích Bollinger Bands
            if "Bollinger_Bands" in indicators:
                bb_decision, bb_confidence, bb_reason = self._analyze_bollinger_bands(
                    indicators["Bollinger_Bands"], current_price
                )
                signals.append(bb_decision)
                confidence_scores.append(bb_confidence)
                reasoning_points.append(bb_reason)

            # Tính toán quyết định cuối cùng
            if not signals:
                return TradingDecision(
                    symbol=symbol,
                    decision=TradeType.HOLD,
                    timeframe=TradeTimeframe.SHORT_TERM,
                    confidence=0.0,
                    entry_price=current_price,
                    reasoning="Không có đủ chỉ báo để phân tích",
                )

            # Xác định quyết định dựa trên đa số
            buy_count = signals.count(TradeType.BUY)
            sell_count = signals.count(TradeType.SELL)
            hold_count = signals.count(TradeType.HOLD)

            if buy_count > sell_count and buy_count > hold_count:
                final_decision = TradeType.BUY
            elif sell_count > buy_count and sell_count > hold_count:
                final_decision = TradeType.SELL
            else:
                final_decision = TradeType.HOLD

            # Tính toán độ tin cậy trung bình
            avg_confidence = sum(confidence_scores) / len(confidence_scores)

            # Xác định khung thời gian dựa trên độ tin cậy
            if avg_confidence > 0.8:
                timeframe = TradeTimeframe.LONG_TERM
            elif avg_confidence > 0.6:
                timeframe = TradeTimeframe.MEDIUM_TERM
            else:
                timeframe = TradeTimeframe.SHORT_TERM

            # Tính toán stop loss và take profit
            stop_loss, take_profit = self._calculate_stop_loss_take_profit(
                final_decision, current_price, indicators
            )

            return TradingDecision(
                symbol=symbol,
                decision=final_decision,
                timeframe=timeframe,
                confidence=avg_confidence,
                entry_price=current_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                reasoning="\n".join(reasoning_points),
                indicators=indicators,
            )

        except Exception as e:
            self.logger.error(f"Lỗi phân tích chỉ báo: {str(e)}")
            return TradingDecision(
                symbol=symbol,
                decision=TradeType.HOLD,
                timeframe=TradeTimeframe.SHORT_TERM,
                confidence=0.0,
                entry_price=current_price,
                reasoning=f"Lỗi phân tích: {str(e)}",
            )

    def _analyze_rsi(self, rsi_data: Dict[str, Any]) -> tuple[TradeType, float, str]:
        """Phân tích chỉ báo RSI."""
        value = rsi_data["value"]
        signal = rsi_data["signal"]

        if value < 30:
            return TradeType.BUY, 0.8, f"RSI quá bán ({value:.2f})"
        elif value > 70:
            return TradeType.SELL, 0.8, f"RSI quá mua ({value:.2f})"
        else:
            return TradeType.HOLD, 0.5, f"RSI trung tính ({value:.2f})"

    def _analyze_macd(self, macd_data: Dict[str, Any]) -> tuple[TradeType, float, str]:
        """Phân tích chỉ báo MACD."""
        value = macd_data["value"]
        signal = macd_data["signal"]
        histogram = macd_data["histogram"]
        crossover = macd_data["crossover"]
        momentum = macd_data["momentum"]

        confidence = 0.7
        if crossover != "None":
            confidence = 0.9

        if crossover == "Bullish" or (value > signal and momentum == "Increasing"):
            return (
                TradeType.BUY,
                confidence,
                f"MACD tín hiệu mua (Crossover: {crossover}, Momentum: {momentum})",
            )
        elif crossover == "Bearish" or (value < signal and momentum == "Decreasing"):
            return (
                TradeType.SELL,
                confidence,
                f"MACD tín hiệu bán (Crossover: {crossover}, Momentum: {momentum})",
            )
        else:
            return (
                TradeType.HOLD,
                0.5,
                f"MACD trung tính (Value: {value:.5f}, Signal: {signal:.5f})",
            )

    def _analyze_bollinger_bands(
        self, bb_data: Dict[str, Any], current_price: float
    ) -> tuple[TradeType, float, str]:
        """Phân tích chỉ báo Bollinger Bands."""
        position = bb_data["position"]
        squeeze = bb_data["squeeze"]
        volatility = bb_data["volatility"]

        if position == "Lower" and squeeze == "Yes":
            return (
                TradeType.BUY,
                0.8,
                f"BB tín hiệu mua (Vị trí: {position}, Co lại: {squeeze})",
            )
        elif position == "Upper" and squeeze == "Yes":
            return (
                TradeType.SELL,
                0.8,
                f"BB tín hiệu bán (Vị trí: {position}, Co lại: {squeeze})",
            )
        else:
            return (
                TradeType.HOLD,
                0.5,
                f"BB trung tính (Vị trí: {position}, Biến động: {volatility})",
            )

    def _calculate_stop_loss_take_profit(
        self, decision: TradeType, current_price: float, indicators: Dict[str, Any]
    ) -> tuple[float, float]:
        """Tính toán stop loss và take profit."""
        if "Bollinger_Bands" in indicators:
            bb = indicators["Bollinger_Bands"]
            if decision == TradeType.BUY:
                stop_loss = bb["BB_Lower"]
                take_profit = current_price + (current_price - stop_loss) * 2
            elif decision == TradeType.SELL:
                stop_loss = bb["BB_Upper"]
                take_profit = current_price - (stop_loss - current_price) * 2
            else:
                return None, None
        else:
            # Tính toán dựa trên ATR nếu có
            atr = indicators.get("ATR", {}).get("value", current_price * 0.01)
            if decision == TradeType.BUY:
                stop_loss = current_price - atr * 2
                take_profit = current_price + atr * 4
            elif decision == TradeType.SELL:
                stop_loss = current_price + atr * 2
                take_profit = current_price - atr * 4
            else:
                return None, None

        return stop_loss, take_profit
