from typing import Dict, Any, Optional
import logging
from datetime import datetime
from ..clients.gemini_client import GeminiClient
from trading.utils.indicators import TechnicalIndicators


class MarketAIAnalyzer:
    def __init__(self, logger: Optional[logging.Logger] = None):
        """Khởi tạo MarketAIAnalyzer

        Args:
            logger: Logger để ghi log
        """
        self.logger = logger or logging.getLogger(__name__)
        self.client = GeminiClient()

    def analyze_market(
        self, symbol: str, indicators: Dict[str, Any], current_price: float
    ) -> Dict[str, Any]:
        """Phân tích thị trường dựa trên các chỉ báo

        Args:
            symbol: Cặp tiền tệ
            indicators: Dict chứa các chỉ báo
            current_price: Giá hiện tại

        Returns:
            Dict chứa kết quả phân tích
        """
        return self.client.analyze_market(symbol, indicators, current_price)

    def analyze_market_with_timeframes(
        self,
        symbol: str,
        timeframe_indicators: Dict[str, Dict[str, Any]],
        current_price: float,
    ) -> Dict[str, Any]:
        """<PERSON>ân tích thị trường dựa trên các chỉ báo từ nhiều khung thời gian

        Args:
            symbol: Cặp tiền tệ
            timeframe_indicators: Dict chứa các chỉ báo cho từng khung thời gian
            current_price: Giá hiện tại

        Returns:
            Dict chứa kết quả phân tích
        """
        # Bổ sung phân tích với các chỉ báo nâng cao
        enhanced_indicators = self._enhance_indicators_analysis(
            timeframe_indicators, current_price
        )

        return self.client.analyze_market_with_timeframes(
            symbol, enhanced_indicators, current_price
        )

    def _enhance_indicators_analysis(
        self, timeframe_indicators: Dict[str, Dict[str, Any]], current_price: float
    ) -> Dict[str, Dict[str, Any]]:
        """Bổ sung phân tích chỉ báo nâng cao cho từng khung thời gian

        Args:
            timeframe_indicators: Dict chứa các chỉ báo gốc theo khung thời gian
            current_price: Giá hiện tại

        Returns:
            Dict chứa các chỉ báo đã được bổ sung phân tích
        """
        enhanced_indicators = timeframe_indicators.copy()

        for timeframe, indicators in timeframe_indicators.items():
            self.logger.info(
                f"Đang bổ sung phân tích chỉ báo nâng cao cho khung thời gian {timeframe}"
            )

            # Đối với mỗi chỉ báo nâng cao, nếu có trong indicators, thêm phân tích chi tiết

            # ADX - Average Directional Index
            if "ADX" in indicators:
                adx_value = indicators["ADX"]
                if isinstance(adx_value, (int, float)) and not isinstance(
                    adx_value, bool
                ):
                    if adx_value < 20:
                        adx_strength = "Xu hướng yếu - thị trường đi ngang"
                    elif adx_value < 30:
                        adx_strength = "Xu hướng trung bình"
                    elif adx_value < 50:
                        adx_strength = "Xu hướng mạnh"
                    else:
                        adx_strength = "Xu hướng rất mạnh"

                    enhanced_indicators[timeframe]["ADX_Analysis"] = {
                        "value": adx_value,
                        "strength": adx_strength,
                        "interpretation": "ADX không cho biết hướng của xu hướng mà chỉ đo lường độ mạnh của xu hướng.",
                    }

            # Stochastic
            if "Stoch_K" in indicators and "Stoch_D" in indicators:
                k_value = indicators["Stoch_K"]
                d_value = indicators["Stoch_D"]

                if (
                    isinstance(k_value, (int, float))
                    and isinstance(d_value, (int, float))
                    and not isinstance(k_value, bool)
                    and not isinstance(d_value, bool)
                ):

                    if k_value > 80 and d_value > 80:
                        stoch_signal = "Quá mua - khả năng xu hướng giảm"
                    elif k_value < 20 and d_value < 20:
                        stoch_signal = "Quá bán - khả năng xu hướng tăng"
                    else:
                        stoch_signal = "Trung tính"

                    # Kiểm tra giao cắt
                    if "Stoch_K_prev" in indicators and "Stoch_D_prev" in indicators:
                        k_prev = indicators["Stoch_K_prev"]
                        d_prev = indicators["Stoch_D_prev"]

                        if k_value > d_value and k_prev <= d_prev:
                            crossover = "Giao cắt tăng (Bullish) - Tín hiệu MUA"
                        elif k_value < d_value and k_prev >= d_prev:
                            crossover = "Giao cắt giảm (Bearish) - Tín hiệu BÁN"
                        else:
                            crossover = "Không có giao cắt"
                    else:
                        crossover = "Chưa đủ dữ liệu để xác định giao cắt"

                    enhanced_indicators[timeframe]["Stochastic_Analysis"] = {
                        "K": k_value,
                        "D": d_value,
                        "signal": stoch_signal,
                        "crossover": crossover,
                        "interpretation": "Stochastic Oscillator là chỉ báo momentum, giúp xác định vùng quá mua/quá bán và điểm đảo chiều tiềm năng.",
                    }

            # Ichimoku Cloud
            if "Ichimoku" in indicators:
                ichimoku = indicators["Ichimoku"]

                # Kiểm tra xem các giá trị cần thiết có tồn tại không
                if all(
                    key in ichimoku
                    for key in [
                        "tenkan_sen",
                        "kijun_sen",
                        "senkou_span_a",
                        "senkou_span_b",
                    ]
                ):
                    tenkan = ichimoku["tenkan_sen"]
                    kijun = ichimoku["kijun_sen"]
                    span_a = ichimoku["senkou_span_a"]
                    span_b = ichimoku["senkou_span_b"]

                    # Xác định loại mây
                    if span_a > span_b:
                        cloud_type = "Mây xanh (Bullish)"
                    else:
                        cloud_type = "Mây đỏ (Bearish)"

                    # Vị trí giá so với mây
                    if current_price > max(span_a, span_b):
                        price_position = "Giá trên mây (Bullish)"
                    elif current_price < min(span_a, span_b):
                        price_position = "Giá dưới mây (Bearish)"
                    else:
                        price_position = "Giá trong mây (Trung tính - Chờ breakout)"

                    # Giao cắt Tenkan-Kijun
                    if "tenkan_sen_prev" in ichimoku and "kijun_sen_prev" in ichimoku:
                        tenkan_prev = ichimoku["tenkan_sen_prev"]
                        kijun_prev = ichimoku["kijun_sen_prev"]

                        if tenkan > kijun and tenkan_prev <= kijun_prev:
                            tk_cross = (
                                "Giao cắt tăng (Tenkan cắt lên Kijun) - Tín hiệu MUA"
                            )
                        elif tenkan < kijun and tenkan_prev >= kijun_prev:
                            tk_cross = (
                                "Giao cắt giảm (Tenkan cắt xuống Kijun) - Tín hiệu BÁN"
                            )
                        else:
                            tk_cross = "Không có giao cắt Tenkan-Kijun"
                    else:
                        tk_cross = "Chưa đủ dữ liệu để xác định giao cắt Tenkan-Kijun"

                    enhanced_indicators[timeframe]["Ichimoku_Analysis"] = {
                        "cloud_type": cloud_type,
                        "price_position": price_position,
                        "tk_cross": tk_cross,
                        "interpretation": "Ichimoku Cloud cung cấp thông tin về xu hướng, động lượng, và hỗ trợ/kháng cự trong một hệ thống đồ thị hoàn chỉnh.",
                    }

            # Fibonacci Retracements
            if "Fibonacci" in indicators:
                fib = indicators["Fibonacci"]

                # Xác định vị trí giá so với các mức Fibonacci
                fib_position = None
                for level_name in [
                    "level_0",
                    "level_23.6",
                    "level_38.2",
                    "level_50.0",
                    "level_61.8",
                    "level_78.6",
                    "level_100",
                ]:
                    if level_name in fib and current_price >= fib[level_name]:
                        if level_name == "level_0":
                            fib_position = "Trên mức 0% (Breakout tăng)"
                        elif level_name == "level_23.6":
                            fib_position = "Gần mức 23.6%"
                        elif level_name == "level_38.2":
                            fib_position = "Gần mức 38.2%"
                        elif level_name == "level_50.0":
                            fib_position = "Gần mức 50.0%"
                        elif level_name == "level_61.8":
                            fib_position = "Gần mức 61.8% (Golden ratio - quan trọng)"
                        elif level_name == "level_78.6":
                            fib_position = "Gần mức 78.6%"
                        elif level_name == "level_100":
                            fib_position = "Gần mức 100%"
                        break

                if fib_position is None:
                    fib_position = "Dưới mức 100% (Breakout giảm)"

                enhanced_indicators[timeframe]["Fibonacci_Analysis"] = {
                    "levels": fib,
                    "current_position": fib_position,
                    "interpretation": "Fibonacci Retracement xác định các mức hỗ trợ và kháng cự dựa trên tỷ lệ Fibonacci, đặc biệt mức 61.8% thường là mức quan trọng.",
                }

            # VWAP (Volume-Weighted Average Price)
            if "VWAP" in indicators:
                vwap = indicators["VWAP"]

                if isinstance(vwap, (int, float)) and not isinstance(vwap, bool):
                    if current_price > vwap:
                        vwap_signal = "Giá trên VWAP (Bullish) - Tín hiệu tích cực"
                        vwap_distance = (
                            (current_price / vwap) - 1
                        ) * 100  # % trên VWAP
                    else:
                        vwap_signal = "Giá dưới VWAP (Bearish) - Tín hiệu tiêu cực"
                        vwap_distance = (
                            (vwap / current_price) - 1
                        ) * 100  # % dưới VWAP

                    enhanced_indicators[timeframe]["VWAP_Analysis"] = {
                        "value": vwap,
                        "signal": vwap_signal,
                        "distance_percent": vwap_distance,
                        "interpretation": "VWAP là giá trung bình có trọng số theo khối lượng, thường được các nhà đầu tư tổ chức sử dụng làm điểm tham chiếu.",
                    }

            # Pivot Points
            if "Pivot_Points" in indicators:
                pivot = indicators["Pivot_Points"]

                # Xác định mức kháng cự/hỗ trợ gần nhất
                nearest_resistance = None
                nearest_resistance_dist = float("inf")
                for level in ["r1", "r2", "r3"]:
                    if level in pivot and pivot[level] > current_price:
                        dist = pivot[level] - current_price
                        if dist < nearest_resistance_dist:
                            nearest_resistance_dist = dist
                            nearest_resistance = {
                                "level": level.upper(),
                                "value": pivot[level],
                            }

                nearest_support = None
                nearest_support_dist = float("inf")
                for level in ["s1", "s2", "s3"]:
                    if level in pivot and pivot[level] < current_price:
                        dist = current_price - pivot[level]
                        if dist < nearest_support_dist:
                            nearest_support_dist = dist
                            nearest_support = {
                                "level": level.upper(),
                                "value": pivot[level],
                            }

                enhanced_indicators[timeframe]["Pivot_Points_Analysis"] = {
                    "levels": pivot,
                    "nearest_resistance": nearest_resistance,
                    "nearest_support": nearest_support,
                    "interpretation": "Pivot Points là các mức giá được tính toán từ giá cao, thấp và đóng cửa của phiên trước, thường được sử dụng để xác định các mức hỗ trợ và kháng cự tiềm năng.",
                }

            # Parabolic SAR
            if "PSAR" in indicators:
                psar = indicators["PSAR"]

                if isinstance(psar, (int, float)) and not isinstance(psar, bool):
                    if current_price > psar:
                        psar_signal = "Giá trên SAR (Xu hướng tăng)"
                    else:
                        psar_signal = "Giá dưới SAR (Xu hướng giảm)"

                    enhanced_indicators[timeframe]["PSAR_Analysis"] = {
                        "value": psar,
                        "signal": psar_signal,
                        "interpretation": "Parabolic SAR là chỉ báo theo xu hướng và được sử dụng để xác định điểm dừng và đảo chiều của xu hướng.",
                    }

            # Williams %R
            if "Williams_R" in indicators:
                williams_r = indicators["Williams_R"]

                if isinstance(williams_r, (int, float)) and not isinstance(
                    williams_r, bool
                ):
                    if williams_r > -20:
                        williams_signal = "Quá mua (> -20) - Khả năng đảo chiều giảm"
                    elif williams_r < -80:
                        williams_signal = "Quá bán (< -80) - Khả năng đảo chiều tăng"
                    else:
                        williams_signal = "Trung tính"

                    enhanced_indicators[timeframe]["Williams_R_Analysis"] = {
                        "value": williams_r,
                        "signal": williams_signal,
                        "interpretation": "Williams %R là chỉ báo động lượng dao động từ 0 đến -100, giúp xác định vùng quá mua/quá bán và điểm đảo chiều tiềm năng.",
                    }

        return enhanced_indicators
