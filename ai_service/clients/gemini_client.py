import os
import logging
import google.generativeai as genai
from typing import Dict, Any, Optional
import json


class GeminiClient:
    def __init__(self):
        """Khởi tạo Gemini client"""
        self.logger = logging.getLogger(__name__)

        # Lấy API key từ biến môi trường
        api_key = os.getenv("GEMINI_API_KEY")
        if not api_key:
            raise ValueError("Không tìm thấy GEMINI_API_KEY trong biến môi trường")

        # Cấu hình Gemini
        genai.configure(api_key=api_key)

        # Khởi tạo model
        self.model = genai.GenerativeModel("gemini-2.0-flash")

        self.logger.info("Đã khởi tạo Gemini client thành công")

    def chat(self, message: str) -> str:
        """Gửi tin nhắn và nhận phản hồi từ AI

        Args:
            message: Tin nhắn cần gửi

        Returns:
            <PERSON>ản hồi từ AI
        """
        try:
            # <PERSON><PERSON><PERSON> tin nhắn và nhận phản hồi
            response = self.model.generate_content(message)
            return response.text

        except Exception as e:
            self.logger.error(f"Lỗi khi gửi tin nhắn đến Gemini AI: {str(e)}")
            raise

    def analyze_market(
        self, symbol: str, indicators: Dict[str, Any], current_price: float
    ) -> Dict[str, Any]:
        """Phân tích thị trường dựa trên các chỉ báo

        Args:
            symbol: Cặp tiền tệ
            indicators: Dict chứa các chỉ báo
            current_price: Giá hiện tại

        Returns:
            Dict chứa kết quả phân tích
        """
        try:
            # Tạo prompt cho AI
            prompt = f"""
            Bạn là một Senior Portfolio Manager, chuyên gia giao dịch Forex thực chiến, sử dụng tư duy Bayesian xác suất cao cấp. Mọi phân tích và chiến lược phải đạt chuẩn kiểm toán nội bộ quỹ, sẵn sàng bảo vệ trước ủy ban đầu tư.

            🎯 MỤC TIÊU CỐT LÕI

            Giao dịch chỉ được thực hiện khi có tối thiểu 2/3 trụ cột phân tích xác nhận đồng pha

            Ưu tiên bảo toàn vốn và tăng trưởng theo cấp số nhân bền vững

            Tuyệt đối không ra lệnh nếu không thể định lượng được

            Mọi chiến lược phải có logic xác suất rõ ràng, có thể giải trình trước hội đồng quản lý rủi ro

            🔍 Three-Pillar Enhanced Analysis – Phân tích 3 trụ cột nâng cao
            1. 📊 Cơ bản – Global Macro (Real-time Insight)
            Theo dõi chính sách Fed, ECB, BoJ, BoE, RBA qua Dot Plot, OIS, CME Futures

            Phân tích dữ liệu bất ngờ: kỳ vọng – thực tế – số liệu trước đó

            Theo dõi dòng tiền hệ thống: QT/QE, RRP, TGA, Liquidity Index

            Phân tích áp lực real yield (TIPS) → định giá USD/Vàng

            Intermarket: US10Y, 2s10s, DXY, VIX, SP500, BTC → bức tranh dòng tiền toàn cầu

            ✅ Sử dụng dữ liệu từ Bloomberg, TradingEconomics, Fedwatch, Forexfactory để xác nhận macro bias

            2. 📉 Kỹ thuật Smart Money – Entry xác suất cao
            Đa khung thời gian chuẩn quỹ: D1 (xu hướng) → H4 (phase) → H1 (Swing) → M15 (entry)

            Chỉ giao dịch nếu có setup xác suất cao:

            Orderblock – FVG – Trap Volume – Liquidity Sweep

            Pullback + Divergence + Confirmation Volume

            Breakout + Retest có dấu vết tổ chức

            Indicator kỹ thuật:

            EMA 20/50/200, RSI, MACD histogram, VWAP, Bollinger Squeeze

            Volume Profile, Delta Spike, OB Heatmap

            Quản trị rủi ro chuẩn quỹ:

            SL tại swing invalidation hoặc ATR x 1.5

            TP logic theo structure, tối thiểu R:R ≥ 1:2

            Dời SL về entry sau khi đạt 1.2R

            3. 🧠 Tâm lý thị trường & Định vị dòng tiền
            Retail Sentiment từ Myfxbook, IG: ưu tiên đánh ngược khi trap xác nhận

            COT Report: phân tích Commercials vs Non-Commercials

            Orderbook Heatmap: xác định vùng thanh khoản tổ chức

            Cảm xúc thị trường: Fear – Greed – Complacency – Euphoria

            VIX, MOVE Index: định lượng risk appetite toàn cầu

            Smart Money Flow Index (SMFI) nếu có → ưu tiên tham khảo

            🖼️ Phân tích biểu đồ ảnh (gửi bởi user)
            Xác định phase thị trường (trend – range – tích lũy)

            Phát hiện dấu vết tổ chức: stop-hunt, volume trap, liquidity sweep

            Entry rõ ràng, có vùng invalidation cụ thể, TP hợp lý theo structure

            Chỉ đưa ra chiến lược khi ≥ 2/3 trụ cột đồng thuận 

            📋 Mẫu chiến lược chuẩn kiểm toán đầu tư
            Cặp tiền	Khung	Lệnh	Entry	SL	TP	R:R	Lý do kỹ thuật	Xác nhận (Cơ bản / Tâm lý)	Xác suất thắng
            🚨 Luật Giao Dịch Không Khoan Nhượng
            ❌ Không đủ ≥2/3 trụ cột xác nhận → KHÔNG GIAO DỊCH

            ❌ Không entry nếu thiếu trap volume hoặc invalidation rõ ràng

            ❌ R:R < 1:2 hoặc không thể quản trị rủi ro tốt → KHÔNG VÀO LỆNH

            ❌ Không FOMO – Không revenge trade – Không overtrade

            ✅ Chỉ ra lệnh khi logic xác suất rõ ràng + có thể giải trình trước hội đồng rủi ro

            ✅ Nếu chưa có đủ dữ kiện → CHỜ – KHÔNG ÉP LỆNH

            🔐 Cam Kết Chuẩn Portfolio Manager
            Mỗi chiến lược đưa ra là kết quả của quy trình phân tích xác suất cấp quỹ

            Không bao giờ ra quyết định mang tính phỏng đoán

            Sẵn sàng đứng trước CIO hoặc kiểm toán nội bộ để bảo vệ quyết định đầu tư

            Ưu tiên capital preservation trước – sau đó mới là tăng trưởng lợi nhuận

            Phân tích thị trường cho {symbol} với các thông tin sau:
            
            Giá hiện tại: {current_price}
            
            Chỉ báo RSI:
            {indicators.get('RSI', 'Không có dữ liệu')}
            
            Chỉ báo MACD:
            {indicators.get('MACD', 'Không có dữ liệu')}
            
            Bollinger Bands:
            {indicators.get('Bollinger_Bands', 'Không có dữ liệu')}
            
            Moving Averages:
            {indicators.get('Moving_Averages', 'Không có dữ liệu')}
            
            Hãy phân tích và đưa ra:
            1. Xu hướng thị trường
            2. Độ mạnh xu hướng (0-100%)
            3. Phân tích chi tiết
            4. Các mức hỗ trợ/kháng cự
            5. Rủi ro
            6. Khuyến nghị giao dịch
            7. Lý do
            8. Stop loss và take profit (nếu có)
            
            Trả về kết quả theo định dạng JSON với các trường sau:
            - trend: Xu hướng thị trường (Bullish/Bearish/Sideways)
            - trend_strength: Độ mạnh xu hướng (0-100)
            - detailed_analysis: Phân tích chi tiết
            - support_resistance: Các mức hỗ trợ/kháng cự
            - risks: Rủi ro
            - recommendation: Khuyến nghị giao dịch (BUY/SELL/HOLD)
            - reasoning: Lý do
            - stop_loss: Mức stop loss (nếu có)
            - take_profit: Mức take profit (nếu có)
            """

            # Gửi prompt và nhận phản hồi
            response = self.model.generate_content(prompt)
            response_text = response.text
            self.logger.info(
                f"Nhận được phản hồi từ Gemini AI: {response_text[:200]}..."
            )

            # Parse kết quả JSON
            try:
                # Tìm JSON trong phản hồi (loại bỏ các phần không phải JSON)
                json_start = response_text.find("{")
                json_end = response_text.rfind("}") + 1

                if json_start >= 0 and json_end > json_start:
                    json_str = response_text[json_start:json_end]
                    result = json.loads(json_str)

                    # Đảm bảo các trường cần thiết đều tồn tại
                    required_fields = [
                        "trend",
                        "trend_strength",
                        "recommendation",
                        "reasoning",
                    ]
                    for field in required_fields:
                        if field not in result:
                            result[field] = "N/A" if field != "trend_strength" else 0

                    return result
                else:
                    # Không tìm thấy JSON trong phản hồi
                    self.logger.warning("Không tìm thấy JSON trong phản hồi từ AI")
                    return {
                        "trend": "Unknown",
                        "trend_strength": 0,
                        "detailed_analysis": response_text,
                        "recommendation": "HOLD",
                        "reasoning": "Không thể phân tích cấu trúc JSON từ phản hồi AI",
                    }
            except Exception as e:
                self.logger.error(f"Lỗi khi parse kết quả JSON: {str(e)}")
                return {
                    "trend": "Unknown",
                    "trend_strength": 0,
                    "detailed_analysis": response_text,
                    "recommendation": "HOLD",
                    "reasoning": f"Lỗi parse JSON: {str(e)}",
                }

        except Exception as e:
            self.logger.error(f"Lỗi khi phân tích thị trường: {str(e)}")
            raise

    def analyze_market_with_timeframes(
        self,
        symbol: str,
        timeframe_indicators: Dict[str, Dict[str, Any]],
        current_price: float,
    ) -> Dict[str, Any]:
        """Phân tích thị trường dựa trên các chỉ báo từ nhiều khung thời gian

        Args:
            symbol: Cặp tiền tệ
            timeframe_indicators: Dict chứa các chỉ báo cho từng khung thời gian
            current_price: Giá hiện tại

        Returns:
            Dict chứa kết quả phân tích
        """
        try:
            # Tạo mô tả cho từng khung thời gian
            timeframe_descriptions = {}
            for timeframe, indicators in timeframe_indicators.items():
                tf_desc = self._format_timeframe_indicators(
                    timeframe, indicators, current_price
                )
                timeframe_descriptions[timeframe] = tf_desc

            # Tạo prompt cho AI với dữ liệu đa khung thời gian
            prompt = f"""
            Bạn là một chuyên gia giao dịch Forex chuyên nghiệp cấp cao, kết hợp phân tích vĩ mô, kỹ thuật và tâm lý thị trường. Mỗi ngày, bạn phải đưa ra chiến lược giao dịch tối ưu, có xác suất thắng cao, dựa trên phân tích liên thị trường và dữ liệu thực tế mới nhất.

            ⚠️ Mọi đề xuất giao dịch của bạn đều phải đạt tiêu chuẩn chuyên gia và bạn hoàn toàn chịu trách nhiệm 100% với hiệu quả của lệnh đó.

            🔍 Phân tích Đa Chiều (3 trụ cột):
            1. 📊 Phân tích Cơ bản (Fundamentals):
            Chính sách tiền tệ & Forward guidance: Fed, ECB, BOJ, PBOC

            Chênh lệch dữ liệu Thực tế vs Dự báo: GDP, CPI, PCE, Tỷ lệ thất nghiệp, PMI

            Địa chính trị & Rủi ro toàn cầu: xung đột, chiến tranh, trừng phạt, bất ổn tài chính

            Tương quan liên thị trường: DXY, lợi suất trái phiếu, giá vàng, giá dầu, S&P 500, VIX

            2. 📉 Phân tích Kỹ thuật & Quản trị Rủi ro (đa khung M15/H1/H4/D1):
            Công cụ: 
            - Chỉ báo xu hướng: MA, MACD, ADX, Parabolic SAR, Ichimoku Cloud
            - Chỉ báo dao động: RSI, CCI, Stochastic, Williams %R
            - Chỉ báo khối lượng: OBV, MFI, VWAP
            - Mức hỗ trợ/kháng cự: Fibonacci, Pivot Points
            - Mẫu hình giá: Breakouts, Pullbacks, Reversals, Continuation patterns

            Chiến lược: Pullback, Breakout, Fakey, Liquidity Grab

            Quản trị rủi ro nghiêm ngặt:

            SL luôn < 10% kỳ vọng lợi nhuận

            R:R tối thiểu = 1:2

            Có trailing stop hoặc dừng lỗ thích ứng (adaptive SL based on ATR hoặc cấu trúc giá)

            3. 🧠 Tâm lý Thị trường & Hành vi Giá:
            Đánh giá tâm lý đám đông, phản ứng sau tin tức, định vị thị trường (COT, positioning)

            Ưu tiên vào lệnh khi có sự đồng thuận giữa các yếu tố cơ bản và kỹ thuật

            📌 Quy trình phân tích chuẩn chuyên gia:

            Multi-timeframe analysis: Xem xét D1 -> H4 -> H1 -> M15 để xác định:
            - Xu hướng chính (D1, H4)
            - Điểm vào lệnh (H1, M15)
            - Điểm SL/TP (dựa trên cấu trúc giá, Fibonacci, ATR)
            
            📌 Xây dựng Confluence (Điểm hội tụ tín hiệu):
            - Cần ít nhất 3 yếu tố hội tụ để xác nhận lệnh
            - Ví dụ: Xu hướng D1 + Breakout H4 + Fibonacci pullback H1 + RSI divergence M15
            - Xác định vị trí giá so với các level quan trọng (Pivot, Fibonacci, Ichimoku)
            
            📌 Phân tích xác suất cao nhất:
            - Trọng số cho từng chỉ báo tùy theo khung thời gian và giai đoạn thị trường
            - Ví dụ: Xu hướng mạnh → ưu tiên xu hướng; Tích lũy → ưu tiên dao động
            - Kiểm tra mâu thuẫn giữa các khung thời gian và giải quyết chúng

            📌 Đề xuất Giao dịch Chuẩn Chuyên Gia:
            Trình bày kết quả theo bảng sau, mỗi đề xuất đều được xác minh kỹ lưỡng trước khi trình bày:

            Cặp tiền Khung Lệnh Entry SL TP R:R Lý do kỹ thuật Xác nhận
            🔑 Giải thích kỹ lưỡng lý do kỹ thuật (ví dụ: break khỏi vùng kháng cự H4 với volume tăng, retest MA200, phân kỳ RSI, engulfing bullish...)
            🔑 Xác nhận: nêu rõ yếu tố cơ bản hoặc tâm lý ủng hộ lệnh (ví dụ: CPI Mỹ thấp hơn kỳ vọng, Fed dovish, VIX giảm…)

            ✅ Nguyên tắc xử lý lệnh & cập nhật sau vào lệnh:
            R:R luôn ≥ 1:2. Không chấp nhận lệnh R:R thấp.

            Nếu có xung đột giữa cơ bản và kỹ thuật → Ưu tiên yếu tố thời điểm mạnh nhất

            Cập nhật chiến lược nếu:

            Biến động > 25 pip

            Xuất hiện tin tức quan trọng

            Theo dõi lại sau 12h để đánh giá hiệu suất lệnh và ra quyết định quản lý tiếp theo

            🔄 Quản trị Lệnh Thông minh:
            Nếu lệnh có lời > 1R → SL chuyển về Entry

            Nếu thị trường đi ngang > 3 phiên liên tục → Chờ phá vỡ rõ ràng mới vào lệnh

            Tránh giao dịch vào vùng tin có biến động lớn chưa định hướng rõ (ví dụ: 30 phút trước/sau FOMC, NFP...)

            ⚠️ Trách nhiệm & Kỷ luật:
            Bạn không được phép đề xuất lệnh chất lượng thấp, mơ hồ hoặc thiếu xác nhận rõ ràng.

            Nếu bạn sai, bạn chịu hoàn toàn trách nhiệm và phải giải thích cụ thể nguyên nhân để rút kinh nghiệm cho phiên tới.

            Mục tiêu: Tối ưu xác suất chiến thắng, bảo toàn vốn, tăng trưởng bền vững.

            Phân tích thị trường cho {symbol} với dữ liệu từ nhiều khung thời gian:
            
            {' '.join(timeframe_descriptions.values())}
            
            Hãy phân tích dữ liệu từ tất cả các khung thời gian trên để có cái nhìn toàn diện và đưa ra:
            1. Xu hướng thị trường (phân tích từng khung thời gian và xu hướng tổng thể)
            2. Độ mạnh xu hướng (0-100%)
            3. Các mẫu hình giá quan trọng xuất hiện trên nhiều khung thời gian
            4. Các mức hỗ trợ/kháng cự quan trọng từ Pivot Points và Fibonacci
            5. Đánh giá tín hiệu từ các chỉ báo khác nhau (MA, MACD, RSI, Stochastic, ADX, Ichimoku, v.v.)
            6. Tín hiệu giao cắt quan trọng (MACD, Stochastic, MA crossovers, Tenkan-Kijun cross)
            7. Phân tích khối lượng giao dịch (volume, OBV, MFI)
            8. Rủi ro
            9. Khuyến nghị giao dịch
            10. Lý do
            11. Entry, Stop loss và take profit (nếu khuyến nghị giao dịch)
            12. Tỷ lệ risk:reward
            
            Phân tích kỹ sự đồng thuận (confluence) giữa các chỉ báo và khung thời gian. Nếu phát hiện mâu thuẫn giữa các khung thời gian, hãy nêu rõ và giải thích.
            
            Trả về kết quả theo định dạng JSON với các trường sau:
            - trend: Xu hướng thị trường tổng thể (Bullish/Bearish/Sideways)
            - timeframe_trends: Xu hướng của từng khung thời gian (đưa vào một đối tượng JSON)
            - trend_strength: Độ mạnh xu hướng tổng thể (0-100)
            - technical_analysis: Phân tích kỹ thuật chi tiết (đưa vào một đối tượng JSON)
            - key_indicators: Tổng hợp tín hiệu từ các chỉ báo quan trọng (đưa vào một đối tượng JSON)
            - detailed_analysis: Phân tích chi tiết tổng hợp tất cả thông tin
            - support_resistance: Các mức hỗ trợ/kháng cự 
            - risks: Rủi ro
            - recommendation: Khuyến nghị giao dịch 1 trong 3 (BUY/SELL/HOLD)
            - reasoning: Lý do
            - entry_price: Giá entry (nếu có) là một con số
            - stop_loss: Mức stop loss (nếu có) là một con số
            - take_profit: Mức take profit (nếu có) là một con số
            - risk_reward_ratio: Tỷ lệ risk:reward (nếu có) dưới dạng "1:X"
            - timeframe_conflicts: Mô tả các mâu thuẫn giữa các khung thời gian (nếu có)
            - recommended_timeframe: Khung thời gian phù hợp nhất cho giao dịch (nếu khuyến nghị giao dịch)
            """

            # Gửi prompt và nhận phản hồi
            response = self.model.generate_content(prompt)
            response_text = response.text
            self.logger.info(
                f"Nhận được phản hồi đa khung thời gian từ Gemini AI: {response_text[:200]}..."
            )

            # Parse kết quả JSON
            try:
                # Tìm JSON trong phản hồi (loại bỏ các phần không phải JSON)
                json_start = response_text.find("{")
                json_end = response_text.rfind("}") + 1

                if json_start >= 0 and json_end > json_start:
                    json_str = response_text[json_start:json_end]
                    result = json.loads(json_str)

                    # Đảm bảo các trường cần thiết đều tồn tại
                    required_fields = [
                        "trend",
                        "trend_strength",
                        "recommendation",
                        "reasoning",
                    ]
                    for field in required_fields:
                        if field not in result:
                            result[field] = "N/A" if field != "trend_strength" else 0

                    return result
                else:
                    # Không tìm thấy JSON trong phản hồi
                    self.logger.warning("Không tìm thấy JSON trong phản hồi từ AI")
                    return {
                        "trend": "Unknown",
                        "trend_strength": 0,
                        "detailed_analysis": response_text,
                        "recommendation": "HOLD",
                        "reasoning": "Không thể phân tích cấu trúc JSON từ phản hồi AI",
                    }
            except Exception as e:
                self.logger.error(
                    f"Lỗi khi parse kết quả JSON từ phân tích đa khung thời gian: {str(e)}"
                )
                return {
                    "trend": "Unknown",
                    "trend_strength": 0,
                    "detailed_analysis": response_text,
                    "recommendation": "HOLD",
                    "reasoning": f"Lỗi parse JSON: {str(e)}",
                }

        except Exception as e:
            self.logger.error(
                f"Lỗi khi phân tích thị trường đa khung thời gian: {str(e)}"
            )
            raise

    def _format_timeframe_indicators(
        self, timeframe: str, indicators: Dict[str, Any], current_price: float
    ) -> str:
        """Format thông tin chỉ báo cho một khung thời gian cụ thể để trình bày trong prompt

        Args:
            timeframe: Khung thời gian
            indicators: Dict chứa các chỉ báo
            current_price: Giá hiện tại

        Returns:
            Chuỗi mô tả các chỉ báo
        """
        description = f"\n=== Khung thời gian {timeframe} ===\n"

        # Thêm giá hiện tại
        description += f"\nGiá hiện tại: {current_price}\n"

        # Chỉ báo cơ bản
        # RSI
        if "RSI" in indicators:
            rsi = indicators["RSI"]
            if isinstance(rsi, dict) and "value" in rsi:
                description += (
                    f"\nRSI: {rsi['value']:.2f} - {rsi.get('signal', 'N/A')}\n"
                )
            else:
                description += f"\nRSI: {rsi}\n"

        # MACD
        if "MACD" in indicators:
            macd = indicators["MACD"]
            if isinstance(macd, dict):
                description += "\nMACD:\n"
                description += f"  - Giá trị MACD: {macd.get('value', 'N/A')}\n"
                description += f"  - Đường Signal: {macd.get('signal', 'N/A')}\n"
                description += f"  - Histogram: {macd.get('histogram', 'N/A')}\n"
                description += f"  - Xu hướng: {macd.get('trend', 'N/A')}\n"
                if "crossover" in macd:
                    description += f"  - Giao cắt: {macd.get('crossover', 'N/A')}\n"
            else:
                description += f"\nMACD: {macd}\n"

        # Bollinger Bands
        if "Bollinger_Bands" in indicators:
            bb = indicators["Bollinger_Bands"]
            if isinstance(bb, dict):
                description += "\nBollinger Bands:\n"
                description += f"  - Upper: {bb.get('upper', 'N/A')}\n"
                description += f"  - Middle: {bb.get('middle', 'N/A')}\n"
                description += f"  - Lower: {bb.get('lower', 'N/A')}\n"
                description += f"  - Width: {bb.get('width', 'N/A')}\n"
                description += f"  - Position: {bb.get('position', 'N/A')}\n"
                if "squeeze" in bb:
                    description += f"  - Squeeze: {bb.get('squeeze', 'N/A')}\n"
            else:
                description += f"\nBollinger Bands: {bb}\n"

        # Moving Averages
        if "Moving_Averages" in indicators:
            ma = indicators["Moving_Averages"]
            if isinstance(ma, dict):
                description += "\nMoving Averages:\n"
                for key, value in ma.items():
                    if key != "trend":  # Xử lý 'trend' riêng
                        description += f"  - {key}: {value}\n"
                if "trend" in ma:
                    description += f"  - Trend: {ma.get('trend', 'N/A')}\n"
            else:
                description += f"\nMoving Averages: {ma}\n"

        # Stochastic
        if "Stochastic" in indicators:
            stoch = indicators["Stochastic"]
            if isinstance(stoch, dict):
                description += "\nStochastic Oscillator:\n"
                description += f"  - %K: {stoch.get('K', 'N/A')}\n"
                description += f"  - %D: {stoch.get('D', 'N/A')}\n"
                description += f"  - Signal: {stoch.get('signal', 'N/A')}\n"
                if "crossover" in stoch:
                    description += f"  - Crossover: {stoch.get('crossover', 'N/A')}\n"
            elif "Stoch_K" in indicators and "Stoch_D" in indicators:
                description += "\nStochastic Oscillator:\n"
                description += f"  - %K: {indicators['Stoch_K']}\n"
                description += f"  - %D: {indicators['Stoch_D']}\n"

        # ATR
        if "ATR" in indicators:
            description += f"\nATR: {indicators['ATR']}\n"

        # Chỉ báo nâng cao
        # ADX
        if "ADX" in indicators:
            adx = indicators["ADX"]
            if isinstance(adx, dict):
                description += "\nADX (Average Directional Index):\n"
                description += f"  - ADX: {adx.get('value', 'N/A')}\n"
                description += f"  - +DI: {adx.get('+DI', 'N/A')}\n"
                description += f"  - -DI: {adx.get('-DI', 'N/A')}\n"
                description += (
                    f"  - Trend Strength: {adx.get('trend_strength', 'N/A')}\n"
                )
                description += f"  - Direction: {adx.get('trend_direction', 'N/A')}\n"
            else:
                description += f"\nADX: {adx}\n"

            # Thêm thông tin ADX_Analysis nếu có
            if "ADX_Analysis" in indicators:
                adx_analysis = indicators["ADX_Analysis"]
                if isinstance(adx_analysis, dict):
                    description += (
                        f"  - Strength: {adx_analysis.get('strength', 'N/A')}\n"
                    )
                    description += f"  - Interpretation: {adx_analysis.get('interpretation', 'N/A')}\n"

        # CCI
        if "CCI" in indicators:
            cci = indicators["CCI"]
            if isinstance(cci, dict):
                description += "\nCCI (Commodity Channel Index):\n"
                description += f"  - Value: {cci.get('value', 'N/A')}\n"
                description += f"  - Signal: {cci.get('signal', 'N/A')}\n"
            else:
                description += f"\nCCI: {cci}\n"

        # RVI
        if "RVI" in indicators:
            rvi = indicators["RVI"]
            if isinstance(rvi, dict):
                description += "\nRVI (Relative Vigor Index):\n"
                description += f"  - Value: {rvi.get('value', 'N/A')}\n"
                description += f"  - Signal: {rvi.get('signal', 'N/A')}\n"
            else:
                description += f"\nRVI: {rvi}\n"

        # Williams %R
        if "Williams_R" in indicators:
            wr = indicators["Williams_R"]
            if isinstance(wr, dict):
                description += "\nWilliams %R:\n"
                description += f"  - Value: {wr.get('value', 'N/A')}\n"
                description += f"  - Signal: {wr.get('signal', 'N/A')}\n"
            else:
                description += f"\nWilliams %R: {wr}\n"

            # Thêm thông tin Williams_R_Analysis nếu có
            if "Williams_R_Analysis" in indicators:
                wr_analysis = indicators["Williams_R_Analysis"]
                if isinstance(wr_analysis, dict):
                    description += f"  - Signal: {wr_analysis.get('signal', 'N/A')}\n"
                    description += f"  - Interpretation: {wr_analysis.get('interpretation', 'N/A')}\n"

        # MFI
        if "MFI" in indicators:
            mfi = indicators["MFI"]
            if isinstance(mfi, dict):
                description += "\nMFI (Money Flow Index):\n"
                description += f"  - Value: {mfi.get('value', 'N/A')}\n"
                description += f"  - Signal: {mfi.get('signal', 'N/A')}\n"
            else:
                description += f"\nMFI: {mfi}\n"

        # VWAP
        if "VWAP" in indicators:
            vwap = indicators["VWAP"]
            if isinstance(vwap, dict):
                description += "\nVWAP (Volume-Weighted Average Price):\n"
                description += f"  - Value: {vwap.get('value', 'N/A')}\n"
                description += f"  - Signal: {vwap.get('signal', 'N/A')}\n"
            else:
                description += f"\nVWAP: {vwap}\n"

            # Thêm thông tin VWAP_Analysis nếu có
            if "VWAP_Analysis" in indicators:
                vwap_analysis = indicators["VWAP_Analysis"]
                if isinstance(vwap_analysis, dict):
                    description += f"  - Signal: {vwap_analysis.get('signal', 'N/A')}\n"
                    description += f"  - Distance: {vwap_analysis.get('distance_percent', 'N/A')}%\n"
                    description += f"  - Interpretation: {vwap_analysis.get('interpretation', 'N/A')}\n"

        # Parabolic SAR
        if "PSAR" in indicators:
            psar = indicators["PSAR"]
            if isinstance(psar, dict):
                description += "\nParabolic SAR:\n"
                description += f"  - Value: {psar.get('value', 'N/A')}\n"
                description += f"  - Signal: {psar.get('signal', 'N/A')}\n"
                if "trend_change" in psar:
                    description += (
                        f"  - Trend Change: {psar.get('trend_change', 'N/A')}\n"
                    )
            else:
                description += f"\nParabolic SAR: {psar}\n"

            # Thêm thông tin PSAR_Analysis nếu có
            if "PSAR_Analysis" in indicators:
                psar_analysis = indicators["PSAR_Analysis"]
                if isinstance(psar_analysis, dict):
                    description += f"  - Signal: {psar_analysis.get('signal', 'N/A')}\n"
                    description += f"  - Interpretation: {psar_analysis.get('interpretation', 'N/A')}\n"

        # OBV
        if "OBV" in indicators:
            obv = indicators["OBV"]
            if isinstance(obv, dict):
                description += "\nOBV (On-Balance Volume):\n"
                description += f"  - Value: {obv.get('value', 'N/A')}\n"
                description += f"  - Trend: {obv.get('trend', 'N/A')}\n"
            else:
                description += f"\nOBV: {obv}\n"

        # ROC
        if "ROC" in indicators:
            roc = indicators["ROC"]
            if isinstance(roc, dict):
                description += "\nROC (Rate of Change):\n"
                description += f"  - Value: {roc.get('value', 'N/A')}\n"
                description += f"  - Signal: {roc.get('signal', 'N/A')}\n"
            else:
                description += f"\nROC: {roc}\n"

        # Ichimoku Cloud
        if "Ichimoku" in indicators:
            ichimoku = indicators["Ichimoku"]
            if isinstance(ichimoku, dict):
                description += "\nIchimoku Cloud:\n"
                description += f"  - Tenkan-sen: {ichimoku.get('tenkan_sen', 'N/A')}\n"
                description += f"  - Kijun-sen: {ichimoku.get('kijun_sen', 'N/A')}\n"
                description += (
                    f"  - Senkou Span A: {ichimoku.get('senkou_span_a', 'N/A')}\n"
                )
                description += (
                    f"  - Senkou Span B: {ichimoku.get('senkou_span_b', 'N/A')}\n"
                )
                if "chikou_span" in ichimoku:
                    description += (
                        f"  - Chikou Span: {ichimoku.get('chikou_span', 'N/A')}\n"
                    )
                if "cloud_type" in ichimoku:
                    description += (
                        f"  - Cloud Type: {ichimoku.get('cloud_type', 'N/A')}\n"
                    )
                if "price_position" in ichimoku:
                    description += (
                        f"  - Price Position: {ichimoku.get('price_position', 'N/A')}\n"
                    )
                if "tk_cross" in ichimoku:
                    description += f"  - TK Cross: {ichimoku.get('tk_cross', 'N/A')}\n"
            else:
                for key in indicators:
                    if key.startswith("Ichimoku_"):
                        description += f"\n{key}: {indicators[key]}\n"

            # Thêm thông tin Ichimoku_Analysis nếu có
            if "Ichimoku_Analysis" in indicators:
                ichimoku_analysis = indicators["Ichimoku_Analysis"]
                if isinstance(ichimoku_analysis, dict):
                    description += f"  - Cloud Type: {ichimoku_analysis.get('cloud_type', 'N/A')}\n"
                    description += f"  - Price Position: {ichimoku_analysis.get('price_position', 'N/A')}\n"
                    description += (
                        f"  - TK Cross: {ichimoku_analysis.get('tk_cross', 'N/A')}\n"
                    )
                    description += f"  - Interpretation: {ichimoku_analysis.get('interpretation', 'N/A')}\n"

        # Pivot Points
        if "Pivot_Points" in indicators:
            pivot = indicators["Pivot_Points"]
            if isinstance(pivot, dict):
                description += "\nPivot Points:\n"
                description += f"  - Pivot: {pivot.get('pivot', 'N/A')}\n"
                description += f"  - R1: {pivot.get('r1', 'N/A')}\n"
                description += f"  - R2: {pivot.get('r2', 'N/A')}\n"
                description += f"  - R3: {pivot.get('r3', 'N/A')}\n"
                description += f"  - S1: {pivot.get('s1', 'N/A')}\n"
                description += f"  - S2: {pivot.get('s2', 'N/A')}\n"
                description += f"  - S3: {pivot.get('s3', 'N/A')}\n"

            # Thêm thông tin Pivot_Points_Analysis nếu có
            if "Pivot_Points_Analysis" in indicators:
                pivot_analysis = indicators["Pivot_Points_Analysis"]
                if isinstance(pivot_analysis, dict):
                    description += "  - Position Analysis:\n"
                    if "position" in pivot_analysis:
                        description += (
                            f"    * Position: {pivot_analysis.get('position', 'N/A')}\n"
                        )
                    if (
                        "nearest_resistance" in pivot_analysis
                        and pivot_analysis["nearest_resistance"]
                    ):
                        nr = pivot_analysis["nearest_resistance"]
                        description += f"    * Nearest Resistance: {nr.get('level', 'N/A')} at {nr.get('value', 'N/A')}\n"
                    if (
                        "nearest_support" in pivot_analysis
                        and pivot_analysis["nearest_support"]
                    ):
                        ns = pivot_analysis["nearest_support"]
                        description += f"    * Nearest Support: {ns.get('level', 'N/A')} at {ns.get('value', 'N/A')}\n"
                    if "interpretation" in pivot_analysis:
                        description += f"    * Interpretation: {pivot_analysis.get('interpretation', 'N/A')}\n"

        # Fibonacci Levels
        if "Fibonacci" in indicators:
            fib = indicators["Fibonacci"]
            if isinstance(fib, dict):
                description += "\nFibonacci Levels:\n"
                if "level_0" in fib:
                    description += f"  - 0%: {fib.get('level_0', 'N/A')}\n"
                if "level_23.6" in fib:
                    description += f"  - 23.6%: {fib.get('level_23.6', 'N/A')}\n"
                if "level_38.2" in fib:
                    description += f"  - 38.2%: {fib.get('level_38.2', 'N/A')}\n"
                if "level_50.0" in fib:
                    description += f"  - 50.0%: {fib.get('level_50.0', 'N/A')}\n"
                if "level_61.8" in fib:
                    description += (
                        f"  - 61.8%: {fib.get('level_61.8', 'N/A')} (Golden Ratio)\n"
                    )
                if "level_78.6" in fib:
                    description += f"  - 78.6%: {fib.get('level_78.6', 'N/A')}\n"
                if "level_100" in fib:
                    description += f"  - 100%: {fib.get('level_100', 'N/A')}\n"

            # Thêm thông tin Fibonacci_Analysis nếu có
            if "Fibonacci_Analysis" in indicators:
                fib_analysis = indicators["Fibonacci_Analysis"]
                if isinstance(fib_analysis, dict):
                    if "current_position" in fib_analysis:
                        description += f"  - Current Position: {fib_analysis.get('current_position', 'N/A')}\n"
                    if "interpretation" in fib_analysis:
                        description += f"  - Interpretation: {fib_analysis.get('interpretation', 'N/A')}\n"

        return description
