from enum import Enum
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime


class TradeType(Enum):
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


class TradeTimeframe(Enum):
    SHORT_TERM = "SHORT_TERM"  # 1-5 phút
    MEDIUM_TERM = "MEDIUM_TERM"  # 15-30 phút
    LONG_TERM = "LONG_TERM"  # 1-4 giờ


@dataclass
class TradingDecision:
    symbol: str
    decision: TradeType
    timeframe: TradeTimeframe
    confidence: float  # 0-1
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    reasoning: str = ""
    indicators: Dict[str, Any] = None
    timestamp: datetime = None

    def to_dict(self) -> Dict[str, Any]:
        return {
            "symbol": self.symbol,
            "decision": self.decision.value,
            "timeframe": self.timeframe.value,
            "confidence": self.confidence,
            "entry_price": self.entry_price,
            "stop_loss": self.stop_loss,
            "take_profit": self.take_profit,
            "reasoning": self.reasoning,
            "indicators": self.indicators,
            "timestamp": self.timestamp.isoformat() if self.timestamp else None,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "TradingDecision":
        return cls(
            symbol=data["symbol"],
            decision=TradeType(data["decision"]),
            timeframe=TradeTimeframe(data["timeframe"]),
            confidence=data["confidence"],
            entry_price=data["entry_price"],
            stop_loss=data.get("stop_loss"),
            take_profit=data.get("take_profit"),
            reasoning=data.get("reasoning", ""),
            indicators=data.get("indicators"),
            timestamp=(
                datetime.fromisoformat(data["timestamp"])
                if data.get("timestamp")
                else None
            ),
        )
