import os
import logging
from dotenv import load_dotenv
from ai_service.clients.gemini_client import GeminiClient
from ai_service.adapters.market_ai_analyzer import MarketAIAnalyzer

# Cấu hình logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def test_gemini_connection():
    """Test kết nối với Gemini AI"""
    # Load biến môi trường
    load_dotenv()

    # Kiểm tra API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("Không tìm thấy GEMINI_API_KEY trong file .env")
        logger.error("Vui lòng thêm GEMINI_API_KEY vào file .env")
        logger.error(
            "Bạn có thể lấy API key tại: https://makersuite.google.com/app/apikey"
        )
        return

    try:
        # Khởi tạo Gemini client
        client = GeminiClient()
        logger.info("Đã khởi tạo Gemini client thành công")

        # Test gửi tin nhắn đơn giản
        test_message = "Xin chào! Bạn có thể giới thiệu về bản thân không?"
        logger.info(f"Đang gửi tin nhắn test: {test_message}")

        response = client.chat(test_message)
        logger.info("=== Phản hồi từ AI ===")
        logger.info(response)
        logger.info("===================")

        return True

    except Exception as e:
        logger.error(f"Lỗi khi test Gemini AI: {str(e)}")
        return False


def test_market_analysis():
    """Test phân tích thị trường từ AI"""
    # Load biến môi trường
    load_dotenv()

    # Kiểm tra API key
    api_key = os.getenv("GEMINI_API_KEY")
    if not api_key:
        logger.error("Không tìm thấy GEMINI_API_KEY trong file .env")
        logger.error("Vui lòng thêm GEMINI_API_KEY vào file .env")
        return False

    try:
        # Tạo dữ liệu chỉ báo giả định
        symbol = "EURUSD_otc"
        current_price = 1.0765
        indicators = {
            "RSI": {
                "value": 65.32,
                "signal": "Bullish",
            },
            "MACD": {
                "value": 0.00123,
                "signal": 0.00089,
                "histogram": 0.00034,
                "crossover": "Bullish",
            },
            "Bollinger_Bands": {
                "upper": 1.0820,
                "middle": 1.0750,
                "lower": 1.0680,
                "position": "Middle",
                "width": 0.0130,
                "percent_b": 62.14,
            },
            "Moving_Averages": {
                "MA50": 1.0720,
                "MA100": 1.0690,
                "MA200": 1.0650,
                "EMA20": 1.0755,
            },
        }

        # Khởi tạo MarketAIAnalyzer
        ai_analyzer = MarketAIAnalyzer(logger)
        logger.info(f"Đang phân tích {symbol} với AI...")

        # Phân tích thị trường
        result = ai_analyzer.analyze_market(symbol, indicators, current_price)

        # Hiển thị kết quả
        logger.info("=== Kết quả phân tích thị trường ===")
        logger.info(f"Xu hướng: {result.get('trend', 'N/A')}")
        logger.info(f"Độ mạnh xu hướng: {result.get('trend_strength', 0)}")
        logger.info(f"Phân tích chi tiết: {result.get('detailed_analysis', 'N/A')}")
        logger.info(f"Khuyến nghị: {result.get('recommendation', 'N/A')}")
        logger.info(f"Lý do: {result.get('reasoning', 'N/A')}")
        if "stop_loss" in result:
            logger.info(f"Stop Loss: {result['stop_loss']}")
        if "take_profit" in result:
            logger.info(f"Take Profit: {result['take_profit']}")
        logger.info("====================================")

        return True

    except Exception as e:
        logger.error(f"Lỗi khi test phân tích thị trường: {str(e)}")
        return False


if __name__ == "__main__":
    # Test kết nối Gemini AI
    logger.info("Đang test kết nối với Gemini AI...")
    if test_gemini_connection():
        logger.info("Test kết nối thành công!")
    else:
        logger.error("Test kết nối thất bại.")
        exit(1)

    # Test phân tích thị trường
    logger.info("\nĐang test phân tích thị trường...")
    if test_market_analysis():
        logger.info("Test phân tích thị trường thành công!")
    else:
        logger.error("Test phân tích thị trường thất bại.")
        exit(1)
